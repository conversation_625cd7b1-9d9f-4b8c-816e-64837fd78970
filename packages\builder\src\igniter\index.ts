import { dirname, resolve } from "node:path";
import { fileURLToPath } from "node:url";
import { build, createServer } from "vite";

import baseConfig from "./base.config.js";

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

export class Igniter {
  root = __dirname;
  constructor(root: string) {
    this.root = root;
  }

  async start() {
    const server = await createServer({
      root: resolve(this.root),
      forceOptimizeDeps: true,
      ...baseConfig,
    });

    await server.listen();

    server.printUrls();
    server.bindCLIShortcuts({ print: true });
  }

  async build() {
    await build({
      root: resolve(this.root),
      ...baseConfig,
    });
  }

  getProxyConfig() {
    
  }
}
